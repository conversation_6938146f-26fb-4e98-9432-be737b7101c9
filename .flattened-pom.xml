<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.cet.electric</groupId>
    <artifactId>matterhorn-basic-service-parent</artifactId>
    <version>0.0.13</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.cet.eem</groupId>
  <artifactId>virtual-power-plant-resource-manager</artifactId>
  <version>1.0.0.1</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>matrix-vpp-resource-manager-common</module>
    <module>matrix-vpp-resource-manager-core</module>
    <module>matrix-vpp-resource-manager-web</module>
  </modules>
  <properties>
    <revision>1.0.0.1</revision>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.cet.eem</groupId>
        <artifactId>eem-base-fusion-config-sdk</artifactId>
        <version>5.0.13.Alpha</version>
      </dependency>
      <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>fusion-matrix-v2-client</artifactId>
        <version>1.0.4</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
              <pomElements>
                <parent>expand</parent>
                <distributionManagement>remove</distributionManagement>
                <repositories>remove</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <fork>false</fork>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
