// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.config;

import com.google.common.base.Predicates;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * Swagger配置类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    @Value("${swagger.enable:true}")
    private boolean enableSwagger;
    @Value("${info.version:1.2}")
    private String version;
    /**
     * 配置Swagger Docket
     *
     * @return Docket配置
     */
    @Bean
    public Docket customDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .host("localhost:8002")
                .apiInfo(apiInfo())
                .enable(enableSwagger)
                .select()
                .apis(Predicates.or(RequestHandlerSelectors.basePackage("com.cet.electric.vpp.resourcemanager.core.controller")))
                .paths(PathSelectors.any())
                .build().globalOperationParameters(this.getParameterList());
    }
    /**
     * 添加header参数配置
     */
    private List<Parameter> getParameterList() {
        ParameterBuilder clientIdTicket = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        clientIdTicket.name("1")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false).build();
        pars.add(clientIdTicket.build());
        return pars;
    }
    /**
     * 配置API信息
     *
     * @return API信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("虚拟电厂资源管理系统API")
                .description("虚拟电厂(VPP)平台资源管理模块的RESTful API接口文档")
                .version("1.0.0")
                .contact(new Contact("VPP开发团队", "https://www.example.com", "<EMAIL>"))
                .license("MIT License")
                .licenseUrl("https://opensource.org/licenses/MIT")
                .build();
    }
}
// @AI-Generated-end 