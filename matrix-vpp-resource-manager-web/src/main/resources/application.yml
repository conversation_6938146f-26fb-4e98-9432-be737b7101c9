server:
  port: 8002

# Swagger配置
swagger:
  enable: true
  host: localhost:8002

spring:
  redis:
    host: ************
    password: sA123456
    port: 26379
    timeout: 3000ms
    client-type: lettuce
    # Lettuce 连接池配置
    lettuce:
      pool:
        max-active: 16       # 最大连接数
        max-idle: 8          # 最大空闲连接
        min-idle: 4          # 最小空闲连接
        max-wait: 1000       # 获取连接最大等待时间(ms)
      shutdown-timeout: 100  # 关闭超时时间(ms)
    # Redisson 配置（直接使用 spring.redis.redisson 属性）
    redisson:
      config: |
        singleServerConfig:
          address: "redis://************:26379"
          password: sA123456
          database: 12
          connectionMinimumIdleSize: 5
          connectionPoolSize: 32
          idleConnectionTimeout: 30000
          connectTimeout: 5000
          timeout: 5000
          retryAttempts: 1
          retryInterval: 500
        #protocol: RESP3
        # 线程池配置 
        # 增加I/O能力
        nettyThreads: 8
        # 增加处理能力
        threads: 4
        # 序列化配置
        codec: !<org.redisson.codec.JsonJacksonCodec> {}
        # 看门狗超时时间(ms)
        lockWatchdogTimeout: 30000       
        # 启用Lua脚本缓存
        useScriptCache: true
        transportMode: NIO

  application:
    name: roommonitor
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    include: powercloud-roommonitor
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

eureka:
  client:
    service-url:
      defaultZone: http://root:ceiec@************:1001/eureka/  # Eureka服务地址
  instance:
    prefer-ip-address: true # 使用IP地址注册
    ip-address: ************


cet:
  base-service:
    model-service:
      url: ${MODE_PLUS_URL:************:8085}
    device-data-service:
      url: ${DEVICE-DATA-SERVICE:************:5050}
    cloud-auth-service:
      url: ${CLOUD-AUTH-SERVICE:************:2014}
    pec-node-service:
      url: ${PEC-NODE-SERVICE:************:8180}
    notice-service:
      url: ${NOTICE-SERVICE:************:5070}
  feign:
    url:
      videoService: ${VIDEO-MANAGEMENT-SERVICE:************:8082}
      pecNodeService: ${PEC_NODE_SERVICE:************:8180}
  loss:
    alert: true
  powercloud:
    wisdom:
      url: ${WISDOM-OPS:************:8030}